// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Noujoum Store';

  @override
  String get appDescription => 'Mauritanian innovation at your fingertips';

  @override
  String get welcomeTitle => 'Welcome to Noujoum Store';

  @override
  String get welcomeSubtitle => 'Discover the best Mauritanian applications';

  @override
  String get featuredApps => 'Featured Applications';

  @override
  String get allApps => 'All Applications';

  @override
  String get categories => 'Categories';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get sort => 'Sort';

  @override
  String get contactDeveloper => 'Contact Developer';

  @override
  String get share => 'Share';

  @override
  String get addToFavorites => 'Add to Favorites';

  @override
  String get removeFromFavorites => 'Remove from Favorites';

  @override
  String get noResults => 'No results found';

  @override
  String get tryDifferentSearch => 'Try a different search';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get retry => 'Retry';

  @override
  String get email => 'Email';

  @override
  String get phone => 'Phone';

  @override
  String get website => 'Website';

  @override
  String get whatsapp => 'WhatsApp';

  @override
  String get developer => 'Developer';

  @override
  String get category => 'Category';

  @override
  String get rating => 'Rating';

  @override
  String get downloads => 'Downloads';

  @override
  String get dateAdded => 'Date Added';

  @override
  String get screenshots => 'Screenshots';

  @override
  String get description => 'Description';

  @override
  String get tags => 'Tags';

  @override
  String get home => 'Home';

  @override
  String get catalog => 'Catalog';

  @override
  String get favorites => 'Favorites';

  @override
  String get profile => 'Profile';

  @override
  String get errorLoadingApps => 'Error loading applications';

  @override
  String get errorContactingDeveloper => 'Error contacting developer';

  @override
  String get errorSharingApp => 'Error sharing application';

  @override
  String get errorAddingToFavorites => 'Error adding to favorites';

  @override
  String get addedToFavorites => 'Added to favorites';

  @override
  String get removedFromFavorites => 'Removed from favorites';

  @override
  String get appShared => 'Application shared';

  @override
  String get login => 'Login';

  @override
  String get loginSubtitle => 'Connect to publish your applications';

  @override
  String get password => 'Password';

  @override
  String get loginButton => 'Login';

  @override
  String get noAccountYet => 'Don\'t have an account yet?';

  @override
  String get createAccount => 'Create Account';

  @override
  String get continueWithoutAccount => 'Continue without account';

  @override
  String get register => 'Register';

  @override
  String get name => 'Name';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get companyName => 'Company Name';

  @override
  String get bio => 'Bio';

  @override
  String get registerButton => 'Register';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get discoverBestSolutions =>
      'Discover the best Mauritanian digital solutions';

  @override
  String get shareYourInnovation => 'Share your innovation with Mauritania';

  @override
  String get findAppsCreatedByLocalDevelopers =>
      'Find applications and software created by local developers to meet your needs.';

  @override
  String get joinMarketplaceConnectWithClients =>
      'Join our marketplace and connect with potential clients across the country.';

  @override
  String get exploreNow => 'Explore Now';

  @override
  String get startPublishing => 'Start Publishing';

  @override
  String get appsAvailable => 'Apps Available';

  @override
  String get activeDevelopers => 'Active Developers';

  @override
  String get searchMauritanianApps => 'Search Mauritanian applications';

  @override
  String get typeAppNameDeveloperCategory =>
      'Type an application name, developer or category';

  @override
  String get language => 'Language';

  @override
  String get arabic => 'العربية';

  @override
  String get french => 'Français';

  @override
  String get settings => 'Settings';

  @override
  String get pleaseEnterEmail => 'Please enter your email';

  @override
  String get pleaseEnterValidEmail => 'Please enter a valid email';

  @override
  String get pleaseEnterPassword => 'Please enter your password';

  @override
  String get loginSuccessful => 'Login successful!';

  @override
  String loginError(String error) {
    return 'Login error: $error';
  }
}
