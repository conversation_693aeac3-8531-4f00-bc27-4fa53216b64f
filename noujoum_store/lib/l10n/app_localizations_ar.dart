// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'متجر نجوم';

  @override
  String get appDescription => 'الابتكار الموريتاني في متناول يدك';

  @override
  String get welcomeTitle => 'مرحباً بكم في متجر نجوم';

  @override
  String get welcomeSubtitle => 'اكتشف أفضل التطبيقات الموريتانية';

  @override
  String get featuredApps => 'التطبيقات المميزة';

  @override
  String get allApps => 'جميع التطبيقات';

  @override
  String get categories => 'الفئات';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get sort => 'ترتيب';

  @override
  String get contactDeveloper => 'اتصل بالمطور';

  @override
  String get share => 'مشاركة';

  @override
  String get addToFavorites => 'إضافة للمفضلة';

  @override
  String get removeFromFavorites => 'إزالة من المفضلة';

  @override
  String get noResults => 'لم يتم العثور على نتائج';

  @override
  String get tryDifferentSearch => 'جرب بحثاً مختلفاً';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get phone => 'الهاتف';

  @override
  String get website => 'الموقع الإلكتروني';

  @override
  String get whatsapp => 'واتساب';

  @override
  String get developer => 'المطور';

  @override
  String get category => 'الفئة';

  @override
  String get rating => 'التقييم';

  @override
  String get downloads => 'التحميلات';

  @override
  String get dateAdded => 'تاريخ الإضافة';

  @override
  String get screenshots => 'لقطات الشاشة';

  @override
  String get description => 'الوصف';

  @override
  String get tags => 'العلامات';

  @override
  String get home => 'الرئيسية';

  @override
  String get catalog => 'الكتالوج';

  @override
  String get favorites => 'المفضلة';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get errorLoadingApps => 'خطأ في تحميل التطبيقات';

  @override
  String get errorContactingDeveloper => 'خطأ في الاتصال بالمطور';

  @override
  String get errorSharingApp => 'خطأ في مشاركة التطبيق';

  @override
  String get errorAddingToFavorites => 'خطأ في إضافة للمفضلة';

  @override
  String get addedToFavorites => 'تمت الإضافة للمفضلة';

  @override
  String get removedFromFavorites => 'تمت الإزالة من المفضلة';

  @override
  String get appShared => 'تم مشاركة التطبيق';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get loginSubtitle => 'قم بتسجيل الدخول لنشر تطبيقاتك';

  @override
  String get password => 'كلمة المرور';

  @override
  String get loginButton => 'دخول';

  @override
  String get noAccountYet => 'ليس لديك حساب بعد؟';

  @override
  String get createAccount => 'إنشاء حساب';

  @override
  String get continueWithoutAccount => 'المتابعة بدون حساب';

  @override
  String get register => 'التسجيل';

  @override
  String get name => 'الاسم';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get companyName => 'اسم الشركة';

  @override
  String get bio => 'السيرة الذاتية';

  @override
  String get registerButton => 'تسجيل';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟';

  @override
  String get discoverBestSolutions => 'اكتشف أفضل الحلول الرقمية الموريتانية';

  @override
  String get shareYourInnovation => 'شارك ابتكارك مع موريتانيا';

  @override
  String get findAppsCreatedByLocalDevelopers =>
      'اعثر على التطبيقات والبرامج التي أنشأها المطورون المحليون لتلبية احتياجاتك.';

  @override
  String get joinMarketplaceConnectWithClients =>
      'انضم إلى السوق الإلكتروني وتواصل مع العملاء المحتملين في جميع أنحاء البلاد.';

  @override
  String get exploreNow => 'استكشف الآن';

  @override
  String get startPublishing => 'ابدأ النشر';

  @override
  String get appsAvailable => 'التطبيقات المتاحة';

  @override
  String get activeDevelopers => 'المطورون النشطون';

  @override
  String get searchMauritanianApps => 'ابحث في التطبيقات الموريتانية';

  @override
  String get typeAppNameDeveloperCategory =>
      'اكتب اسم التطبيق أو المطور أو الفئة';

  @override
  String get language => 'اللغة';

  @override
  String get arabic => 'العربية';

  @override
  String get french => 'Français';

  @override
  String get settings => 'الإعدادات';

  @override
  String get pleaseEnterEmail => 'يرجى إدخال بريدك الإلكتروني';

  @override
  String get pleaseEnterValidEmail => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get pleaseEnterPassword => 'يرجى إدخال كلمة المرور';

  @override
  String get loginSuccessful => 'تم تسجيل الدخول بنجاح!';

  @override
  String loginError(String error) {
    return 'خطأ في تسجيل الدخول: $error';
  }
}
