{"@@locale": "en", "appName": "Noujoum Store", "@appName": {"description": "The name of the application"}, "appDescription": "Mauritanian innovation at your fingertips", "@appDescription": {"description": "The description of the application"}, "welcomeTitle": "Welcome to Noujoum Store", "@welcomeTitle": {"description": "Welcome title on home screen"}, "welcomeSubtitle": "Discover the best Mauritanian applications", "@welcomeSubtitle": {"description": "Welcome subtitle on home screen"}, "featuredApps": "Featured Applications", "@featuredApps": {"description": "Featured apps section title"}, "allApps": "All Applications", "@allApps": {"description": "All apps section title"}, "categories": "Categories", "@categories": {"description": "Categories section title"}, "search": "Search", "@search": {"description": "Search button/field label"}, "filter": "Filter", "@filter": {"description": "Filter button label"}, "sort": "Sort", "@sort": {"description": "Sort button label"}, "contactDeveloper": "Contact Developer", "@contactDeveloper": {"description": "Contact developer button"}, "share": "Share", "@share": {"description": "Share button"}, "addToFavorites": "Add to Favorites", "@addToFavorites": {"description": "Add to favorites button"}, "removeFromFavorites": "Remove from Favorites", "@removeFromFavorites": {"description": "Remove from favorites button"}, "noResults": "No results found", "@noResults": {"description": "No search results message"}, "tryDifferentSearch": "Try a different search", "@tryDifferentSearch": {"description": "Try different search suggestion"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Generic error message"}, "retry": "Retry", "@retry": {"description": "Retry button"}, "email": "Email", "@email": {"description": "Email label"}, "phone": "Phone", "@phone": {"description": "Phone label"}, "website": "Website", "@website": {"description": "Website label"}, "whatsapp": "WhatsApp", "@whatsapp": {"description": "WhatsApp label"}, "developer": "Developer", "@developer": {"description": "Developer label"}, "category": "Category", "@category": {"description": "Category label"}, "rating": "Rating", "@rating": {"description": "Rating label"}, "downloads": "Downloads", "@downloads": {"description": "Downloads label"}, "dateAdded": "Date Added", "@dateAdded": {"description": "Date added label"}, "screenshots": "Screenshots", "@screenshots": {"description": "Screenshots section title"}, "description": "Description", "@description": {"description": "Description section title"}, "tags": "Tags", "@tags": {"description": "Tags section title"}, "home": "Home", "@home": {"description": "Home navigation tab"}, "catalog": "Catalog", "@catalog": {"description": "Catalog navigation tab"}, "favorites": "Favorites", "@favorites": {"description": "Favorites navigation tab"}, "profile": "Profile", "@profile": {"description": "Profile navigation tab"}, "errorLoadingApps": "Error loading applications", "@errorLoadingApps": {"description": "Error message when apps fail to load"}, "errorContactingDeveloper": "Error contacting developer", "@errorContactingDeveloper": {"description": "Error message when contacting developer fails"}, "errorSharingApp": "Error sharing application", "@errorSharingApp": {"description": "Error message when sharing fails"}, "errorAddingToFavorites": "Error adding to favorites", "@errorAddingToFavorites": {"description": "Error message when adding to favorites fails"}, "addedToFavorites": "Added to favorites", "@addedToFavorites": {"description": "Success message when added to favorites"}, "removedFromFavorites": "Removed from favorites", "@removedFromFavorites": {"description": "Success message when removed from favorites"}, "appShared": "Application shared", "@appShared": {"description": "Success message when app is shared"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login screen title"}, "loginSubtitle": "Connect to publish your applications", "@loginSubtitle": {"description": "Login screen subtitle"}, "password": "Password", "@password": {"description": "Password field label"}, "loginButton": "<PERSON><PERSON>", "@loginButton": {"description": "Login button text"}, "noAccountYet": "Don't have an account yet?", "@noAccountYet": {"description": "No account text on login screen"}, "createAccount": "Create Account", "@createAccount": {"description": "Create account button text"}, "continueWithoutAccount": "Continue without account", "@continueWithoutAccount": {"description": "Continue without account button"}, "register": "Register", "@register": {"description": "Register screen title"}, "name": "Name", "@name": {"description": "Name field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "companyName": "Company Name", "@companyName": {"description": "Company name field label"}, "bio": "Bio", "@bio": {"description": "Bio field label"}, "registerButton": "Register", "@registerButton": {"description": "Register button text"}, "alreadyHaveAccount": "Already have an account?", "@alreadyHaveAccount": {"description": "Already have account text on register screen"}, "discoverBestSolutions": "Discover the best Mauritanian digital solutions", "@discoverBestSolutions": {"description": "Browse mode hero title"}, "shareYourInnovation": "Share your innovation with Mauritania", "@shareYourInnovation": {"description": "Publish mode hero title"}, "findAppsCreatedByLocalDevelopers": "Find applications and software created by local developers to meet your needs.", "@findAppsCreatedByLocalDevelopers": {"description": "Browse mode hero description"}, "joinMarketplaceConnectWithClients": "Join our marketplace and connect with potential clients across the country.", "@joinMarketplaceConnectWithClients": {"description": "Publish mode hero description"}, "exploreNow": "Explore Now", "@exploreNow": {"description": "Explore now button"}, "startPublishing": "Start Publishing", "@startPublishing": {"description": "Start publishing button"}, "appsAvailable": "Apps Available", "@appsAvailable": {"description": "Apps available stats label"}, "activeDevelopers": "Active Developers", "@activeDevelopers": {"description": "Active developers stats label"}, "searchMauritanianApps": "Search Mauritanian applications", "@searchMauritanianApps": {"description": "Search placeholder text"}, "typeAppNameDeveloperCategory": "Type an application name, developer or category", "@typeAppNameDeveloperCategory": {"description": "Search hint text"}, "language": "Language", "@language": {"description": "Language selection label"}, "arabic": "العربية", "@arabic": {"description": "Arabic language name in Arabic"}, "french": "Français", "@french": {"description": "French language name in French"}, "settings": "Settings", "@settings": {"description": "Settings screen title"}, "pleaseEnterEmail": "Please enter your email", "@pleaseEnterEmail": {"description": "Email validation message"}, "pleaseEnterValidEmail": "Please enter a valid email", "@pleaseEnterValidEmail": {"description": "Valid email validation message"}, "pleaseEnterPassword": "Please enter your password", "@pleaseEnterPassword": {"description": "Password validation message"}, "loginSuccessful": "Login successful!", "@loginSuccessful": {"description": "Login success message"}, "loginError": "Login error: {error}", "@loginError": {"description": "Login error message", "placeholders": {"error": {"type": "String", "description": "The error message"}}}}