// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'Noujoum Store';

  @override
  String get appDescription => 'L\'innovation mauritanienne à portée de main';

  @override
  String get welcomeTitle => 'Bienvenue sur Noujoum Store';

  @override
  String get welcomeSubtitle =>
      'Découvrez les meilleures applications mauritaniennes';

  @override
  String get featuredApps => 'Applications en vedette';

  @override
  String get allApps => 'Toutes les applications';

  @override
  String get categories => 'Catégories';

  @override
  String get search => 'Rechercher';

  @override
  String get filter => 'Filtrer';

  @override
  String get sort => 'Trier';

  @override
  String get contactDeveloper => 'Contacter le développeur';

  @override
  String get share => 'Partager';

  @override
  String get addToFavorites => 'Ajouter aux favoris';

  @override
  String get removeFromFavorites => 'Retirer des favoris';

  @override
  String get noResults => 'Aucun résultat trouvé';

  @override
  String get tryDifferentSearch => 'Essayez une recherche différente';

  @override
  String get loading => 'Chargement...';

  @override
  String get error => 'Erreur';

  @override
  String get retry => 'Réessayer';

  @override
  String get email => 'Email';

  @override
  String get phone => 'Téléphone';

  @override
  String get website => 'Site web';

  @override
  String get whatsapp => 'WhatsApp';

  @override
  String get developer => 'Développeur';

  @override
  String get category => 'Catégorie';

  @override
  String get rating => 'Note';

  @override
  String get downloads => 'Téléchargements';

  @override
  String get dateAdded => 'Ajouté le';

  @override
  String get screenshots => 'Captures d\'écran';

  @override
  String get description => 'Description';

  @override
  String get tags => 'Tags';

  @override
  String get home => 'Accueil';

  @override
  String get catalog => 'Catalogue';

  @override
  String get favorites => 'Favoris';

  @override
  String get profile => 'Profil';

  @override
  String get errorLoadingApps => 'Erreur lors du chargement des applications';

  @override
  String get errorContactingDeveloper =>
      'Erreur lors du contact avec le développeur';

  @override
  String get errorSharingApp => 'Erreur lors du partage de l\'application';

  @override
  String get errorAddingToFavorites => 'Erreur lors de l\'ajout aux favoris';

  @override
  String get addedToFavorites => 'Ajouté aux favoris';

  @override
  String get removedFromFavorites => 'Retiré des favoris';

  @override
  String get appShared => 'Application partagée';

  @override
  String get login => 'Connexion';

  @override
  String get loginSubtitle => 'Connectez-vous pour publier vos applications';

  @override
  String get password => 'Mot de passe';

  @override
  String get loginButton => 'Se connecter';

  @override
  String get noAccountYet => 'Pas encore de compte?';

  @override
  String get createAccount => 'Créer un compte';

  @override
  String get continueWithoutAccount => 'Continuer sans compte';

  @override
  String get register => 'Inscription';

  @override
  String get name => 'Nom';

  @override
  String get confirmPassword => 'Confirmer le mot de passe';

  @override
  String get companyName => 'Nom de l\'entreprise';

  @override
  String get bio => 'Biographie';

  @override
  String get registerButton => 'S\'inscrire';

  @override
  String get alreadyHaveAccount => 'Vous avez déjà un compte?';

  @override
  String get discoverBestSolutions =>
      'Découvrez les meilleures solutions digitales mauritaniennes';

  @override
  String get shareYourInnovation =>
      'Partagez votre innovation avec la Mauritanie';

  @override
  String get findAppsCreatedByLocalDevelopers =>
      'Trouvez des applications et logiciels créés par des développeurs locaux pour répondre à vos besoins.';

  @override
  String get joinMarketplaceConnectWithClients =>
      'Rejoignez notre marketplace et connectez-vous avec des clients potentiels à travers le pays.';

  @override
  String get exploreNow => 'Explorer maintenant';

  @override
  String get startPublishing => 'Commencer à publier';

  @override
  String get appsAvailable => 'Apps disponibles';

  @override
  String get activeDevelopers => 'Développeurs actifs';

  @override
  String get searchMauritanianApps =>
      'Recherchez des applications mauritaniennes';

  @override
  String get typeAppNameDeveloperCategory =>
      'Tapez le nom d\'une application, développeur ou catégorie';

  @override
  String get language => 'Langue';

  @override
  String get arabic => 'العربية';

  @override
  String get french => 'Français';

  @override
  String get settings => 'Paramètres';

  @override
  String get pleaseEnterEmail => 'Veuillez saisir votre email';

  @override
  String get pleaseEnterValidEmail => 'Veuillez saisir un email valide';

  @override
  String get pleaseEnterPassword => 'Veuillez saisir votre mot de passe';

  @override
  String get loginSuccessful => 'Connexion réussie!';

  @override
  String loginError(String error) {
    return 'Erreur de connexion: $error';
  }
}
