<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AppController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\SubscriptionController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\SubscriptionController as AdminSubscriptionController;
use App\Http\Controllers\Admin\SettingsController;

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/verify-email', [AuthController::class, 'verifyEmailAndCompleteRegistration']);
Route::post('/resend-verification', [AuthController::class, 'resendVerificationCode']);
Route::post('/login', [AuthController::class, 'login']);

// Public app routes
Route::get('/apps', [AppController::class, 'index']);
Route::get('/apps/{id}', [AppController::class, 'show']);
Route::get('/apps/featured/list', [AppController::class, 'featured']);
Route::get('/apps/top-rated/list', [AppController::class, 'topRated']);
Route::get('/apps/most-downloaded/list', [AppController::class, 'mostDownloaded']);

// Public category routes
Route::get('/categories', [CategoryController::class, 'index']);
Route::get('/categories/{id}', [CategoryController::class, 'show']);

// Public stats route
Route::get('/stats', [AppController::class, 'stats']);

// Public subscription routes
Route::get('/subscription/packages', [SubscriptionController::class, 'packages']);
Route::get('/subscription/payment-info', [SubscriptionController::class, 'paymentInfo']);
Route::get('/subscription/settings', [SubscriptionController::class, 'settings']);

// Debug route to test connectivity
Route::get('/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is working!',
        'timestamp' => now(),
    ]);
});

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/profile', [AuthController::class, 'profile']);
    Route::put('/profile', [AuthController::class, 'updateProfile']);

    // File upload routes
    Route::post('/upload', [App\Http\Controllers\Api\UploadController::class, 'upload']);
    Route::post('/upload-multiple', [App\Http\Controllers\Api\UploadController::class, 'uploadMultiple']);
    Route::delete('/upload', [App\Http\Controllers\Api\UploadController::class, 'delete']);
    
    // User app management
    Route::get('/my-apps', [AppController::class, 'myApps']);
    Route::post('/apps', [AppController::class, 'store']);
    Route::put('/apps/{id}', [AppController::class, 'update']);
    Route::delete('/apps/{id}', [AppController::class, 'destroy']);

    // User subscription management
    Route::get('/subscription/status', [SubscriptionController::class, 'status']);
    Route::post('/subscription/payment', [SubscriptionController::class, 'createPayment']);
    Route::get('/subscription/transactions', [SubscriptionController::class, 'transactions']);
    Route::get('/subscription/transactions/{id}', [SubscriptionController::class, 'transaction']);
    Route::post('/subscription/transactions/{id}/cancel', [SubscriptionController::class, 'cancelTransaction']);
    
    // Admin routes
    Route::prefix('admin')->group(function () {
        Route::get('/stats', [DashboardController::class, 'stats']);
        Route::get('/users', [DashboardController::class, 'users']);
        Route::get('/apps', [DashboardController::class, 'apps']);
        Route::post('/apps/{id}/approve', [DashboardController::class, 'approveApp']);
        Route::post('/apps/{id}/reject', [DashboardController::class, 'rejectApp']);
        Route::post('/apps/{id}/toggle-feature', [DashboardController::class, 'toggleFeatureApp']);
        Route::post('/users/{id}/toggle-verify', [DashboardController::class, 'toggleVerifyUser']);
        Route::delete('/users/{id}', [DashboardController::class, 'deleteUser']);
        
        // Category management
        Route::post('/categories', [CategoryController::class, 'store']);
        Route::put('/categories/{id}', [CategoryController::class, 'update']);
        Route::delete('/categories/{id}', [CategoryController::class, 'destroy']);

        // Subscription management
        Route::get('/subscription/stats', [AdminSubscriptionController::class, 'stats']);
        Route::get('/subscription/packages', [AdminSubscriptionController::class, 'packages']);
        Route::post('/subscription/packages', [AdminSubscriptionController::class, 'createPackage']);
        Route::put('/subscription/packages/{id}', [AdminSubscriptionController::class, 'updatePackage']);
        Route::delete('/subscription/packages/{id}', [AdminSubscriptionController::class, 'deletePackage']);
        Route::get('/subscription/transactions', [AdminSubscriptionController::class, 'transactions']);
        Route::post('/subscription/transactions/{id}/approve', [AdminSubscriptionController::class, 'approveTransaction']);
        Route::post('/subscription/transactions/{id}/reject', [AdminSubscriptionController::class, 'rejectTransaction']);

        // Settings management
        Route::get('/settings', [SettingsController::class, 'index']);
        Route::get('/settings/group/{group}', [SettingsController::class, 'byGroup']);
        Route::post('/settings', [SettingsController::class, 'store']);
        Route::put('/settings/{id}', [SettingsController::class, 'update']);
        Route::delete('/settings/{id}', [SettingsController::class, 'destroy']);
        Route::post('/settings/bulk-update', [SettingsController::class, 'updateBulk']);
        Route::get('/settings/public', [SettingsController::class, 'publicSettings']);
    });
});
