<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use App\Models\User;
use App\Models\Category;

class AppCreationWithFilesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a category for testing
        Category::factory()->create([
            'id' => 1,
            'name' => 'Business',
            'name_arabic' => 'الأعمال',
            'description' => 'Business applications',
            'icon' => 'business',
            'color' => '#007bff',
            'subcategories' => ['CRM', 'Accounting'],
            'is_active' => true,
        ]);
    }

    public function test_user_can_create_app_with_file_uploads()
    {
        // Create a user with subscription
        $user = User::factory()->create([
            'subscription_status' => 'active',
            'subscription_expires_at' => now()->addMonths(1),
        ]);

        // Create fake files
        Storage::fake('public');
        $iconFile = UploadedFile::fake()->image('app-icon.jpg', 150, 150);
        $screenshotFiles = [
            UploadedFile::fake()->image('screenshot1.jpg', 300, 600),
            UploadedFile::fake()->image('screenshot2.jpg', 300, 600),
        ];

        // App data
        $appData = [
            'app_name' => 'Test App',
            'tagline' => 'A test application',
            'description' => 'This is a test application for file upload testing',
            'detailed_description' => 'Detailed description of the test app',
            'app_type' => 'mobile',
            'supported_platforms' => ['android', 'iOS'],
            'current_version' => '1.0.0',
            'icon_file' => $iconFile,
            'screenshot_files' => $screenshotFiles,
            'license_type' => 'free',
            'pricing_model' => 'free',
            'pricing' => 'Free',
            'has_free_trial' => false,
            'trial_days' => 0,
            'is_open_source' => false,
            'target_audience' => 'Individual',
            'business_sectors' => ['Technology'],
            'business_value' => 'Provides great value for testing',
            'key_features' => ['Feature 1', 'Feature 2'],
            'technical_requirements' => 'Android 8.0+',
            'has_documentation' => false,
            'support_options' => ['email'],
            'languages' => ['English'],
            'category_id' => 1,
            'subcategory' => 'Mobile Apps',
            'tags' => ['test', 'mobile'],
        ];

        // Make authenticated request to create app
        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/apps', $appData);

        // Assert successful response
        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'app_name',
                    'icon_url',
                    'screenshots',
                ]
            ]);

        // Assert app was created in database
        $this->assertDatabaseHas('apps', [
            'app_name' => 'Test App',
            'user_id' => $user->id,
        ]);

        // Assert files were uploaded and URLs are stored
        $responseData = $response->json('data');
        $this->assertNotEmpty($responseData['icon_url']);
        $this->assertCount(2, $responseData['screenshots']);

        // Assert files exist in storage
        $app = \App\Models\App::where('app_name', 'Test App')->first();
        $this->assertNotNull($app);
        
        // Extract path from URL and check if file exists
        $iconPath = str_replace('/storage/', '', parse_url($app->icon_url, PHP_URL_PATH));
        Storage::disk('public')->assertExists($iconPath);

        foreach ($app->screenshots as $screenshotUrl) {
            $screenshotPath = str_replace('/storage/', '', parse_url($screenshotUrl, PHP_URL_PATH));
            Storage::disk('public')->assertExists($screenshotPath);
        }
    }

    public function test_app_creation_requires_either_file_or_url_for_icon()
    {
        $user = User::factory()->create([
            'subscription_status' => 'active',
            'subscription_expires_at' => now()->addMonths(1),
        ]);

        Storage::fake('public');

        $appData = [
            'app_name' => 'Test App',
            'tagline' => 'A test application',
            'description' => 'This is a test application',
            'app_type' => 'mobile',
            'supported_platforms' => ['android'],
            'license_type' => 'free',
            'pricing_model' => 'free',
            'pricing' => 'Free',
            'target_audience' => 'Individual',
            'business_sectors' => ['Technology'],
            'business_value' => 'Test value',
            'key_features' => ['Feature 1'],
            'support_options' => ['email'],
            'languages' => ['English'],
            'category_id' => 1,
            'subcategory' => 'Mobile Apps',
            'tags' => ['test'],
            'screenshots' => ['https://example.com/screenshot.jpg'],
            // No icon_file or icon_url provided
        ];

        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/apps', $appData);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Either icon file or icon URL is required'
            ]);
    }

    public function test_app_creation_with_url_fallback()
    {
        $user = User::factory()->create([
            'subscription_status' => 'active',
            'subscription_expires_at' => now()->addMonths(1),
        ]);

        $appData = [
            'app_name' => 'Test App with URLs',
            'tagline' => 'A test application',
            'description' => 'This is a test application with URL fallback',
            'app_type' => 'mobile',
            'supported_platforms' => ['android'],
            'icon_url' => 'https://example.com/icon.jpg',
            'screenshots' => ['https://example.com/screenshot1.jpg', 'https://example.com/screenshot2.jpg'],
            'license_type' => 'free',
            'pricing_model' => 'free',
            'pricing' => 'Free',
            'target_audience' => 'Individual',
            'business_sectors' => ['Technology'],
            'business_value' => 'Test value',
            'key_features' => ['Feature 1'],
            'support_options' => ['email'],
            'languages' => ['English'],
            'category_id' => 1,
            'subcategory' => 'Mobile Apps',
            'tags' => ['test'],
        ];

        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/apps', $appData);

        $response->assertStatus(201);
        
        $app = \App\Models\App::where('app_name', 'Test App with URLs')->first();
        $this->assertEquals('https://example.com/icon.jpg', $app->icon_url);
        $this->assertEquals(['https://example.com/screenshot1.jpg', 'https://example.com/screenshot2.jpg'], $app->screenshots);
    }
}
