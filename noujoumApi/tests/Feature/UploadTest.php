<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;
use App\Models\User;

class UploadTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_upload_file()
    {
        // Create a user
        $user = User::factory()->create();

        // Create a fake image file
        Storage::fake('public');
        $file = UploadedFile::fake()->image('test-icon.jpg', 100, 100);

        // Make authenticated request to upload endpoint
        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/upload', [
                'file' => $file,
                'folder' => 'app-icons'
            ]);

        // Assert successful response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'url',
                    'path',
                    'filename',
                    'original_name',
                    'size',
                    'mime_type'
                ]
            ]);

        // Assert file was stored
        $responseData = $response->json('data');
        Storage::disk('public')->assertExists($responseData['path']);
    }

    public function test_user_can_upload_multiple_files()
    {
        // Create a user
        $user = User::factory()->create();

        // Create fake image files
        Storage::fake('public');
        $files = [
            UploadedFile::fake()->image('screenshot1.jpg', 300, 600),
            UploadedFile::fake()->image('screenshot2.jpg', 300, 600),
            UploadedFile::fake()->image('screenshot3.jpg', 300, 600),
        ];

        // Make authenticated request to upload multiple files
        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/upload-multiple', [
                'files' => $files,
                'folder' => 'app-screenshots'
            ]);

        // Assert successful response
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'url',
                        'path',
                        'filename',
                        'original_name',
                        'size',
                        'mime_type'
                    ]
                ]
            ]);

        // Assert all files were stored
        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);
        
        foreach ($responseData as $fileData) {
            Storage::disk('public')->assertExists($fileData['path']);
        }
    }

    public function test_upload_requires_authentication()
    {
        Storage::fake('public');
        $file = UploadedFile::fake()->image('test.jpg');

        $response = $this->postJson('/api/upload', [
            'file' => $file,
            'folder' => 'app-icons'
        ]);

        $response->assertStatus(401);
    }

    public function test_upload_validates_file_type()
    {
        $user = User::factory()->create();
        Storage::fake('public');
        
        // Try to upload a non-image file
        $file = UploadedFile::fake()->create('document.pdf', 100);

        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/upload', [
                'file' => $file,
                'folder' => 'app-icons'
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['file']);
    }

    public function test_upload_validates_file_size()
    {
        $user = User::factory()->create();
        Storage::fake('public');
        
        // Try to upload a file larger than 5MB
        $file = UploadedFile::fake()->image('large.jpg')->size(6000); // 6MB

        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/upload', [
                'file' => $file,
                'folder' => 'app-icons'
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['file']);
    }
}
