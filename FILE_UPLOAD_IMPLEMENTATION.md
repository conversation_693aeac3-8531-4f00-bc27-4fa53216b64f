# File Upload Implementation for App Publishing

## Overview

This implementation adds file upload functionality to the Noujoum Store app publishing system, allowing users to upload app icons and screenshots directly instead of providing URLs.

## Backend Changes

### 1. Upload Controller (`noujoumApi/app/Http/Controllers/Api/UploadController.php`)

New controller with three main endpoints:
- `POST /api/upload` - Upload single file
- `POST /api/upload-multiple` - Upload multiple files
- `DELETE /api/upload` - Delete uploaded file

**Features:**
- File validation (JPEG, PNG, JPG, GIF, WebP)
- Size limit: 5MB per file
- Organized storage in folders (app-icons, app-screenshots, etc.)
- Unique filename generation
- Full URL generation for frontend consumption

### 2. App Controller Updates (`noujoumApi/app/Http/Controllers/Api/AppController.php`)

**Enhanced validation:**
- Accepts both file uploads and URL fallbacks
- `icon_file` OR `icon_url` required
- `screenshot_files` OR `screenshots` required
- Custom validation logic for either/or requirements

**File processing:**
- Automatic file upload handling during app creation
- Generates storage URLs for uploaded files
- Maintains backward compatibility with URL-based submissions

### 3. API Routes (`noujoumApi/routes/api.php`)

New protected routes:
```php
Route::post('/upload', [UploadController::class, 'upload']);
Route::post('/upload-multiple', [UploadController::class, 'uploadMultiple']);
Route::delete('/upload', [UploadController::class, 'delete']);
```

## Frontend Changes

### 1. Publish App Screen (`noujoum_store/lib/screens/publish_app_screen.dart`)

**Icon Upload:**
- Image picker integration
- Real-time upload with progress indicator
- Preview of uploaded icon
- Replace functionality

**Screenshot Upload:**
- Multiple image selection
- Batch upload processing
- Visual gallery with remove functionality
- Maximum 10 screenshots limit
- Horizontal scrollable preview

**Enhanced Validation:**
- File-based validation instead of URL validation
- Real-time feedback for upload status
- Error handling for upload failures

### 2. App Detail Screen (`noujoum_store/lib/screens/app_detail_screen.dart`)

**Updated Icon Display:**
- CachedNetworkImage integration
- Proper error handling and placeholders
- Support for both uploaded files and URL fallbacks

### 3. App Card Widget (`noujoum_store/lib/widgets/app_card.dart`)

Already properly implemented with CachedNetworkImage for icon display.

## Storage Configuration

### Laravel Storage Setup

**File Storage:**
- Uses Laravel's `public` disk
- Files stored in `storage/app/public/`
- Organized in subfolders: `app-icons/`, `app-screenshots/`
- Requires `php artisan storage:link` for public access

**URL Generation:**
- Full URLs generated using `Storage::url()`
- Compatible with both local and cloud storage
- Consistent URL format across the application

## Database Schema

No changes required - existing schema already supports:
- `icon_url` (string) - stores file URL or external URL
- `screenshots` (JSON) - stores array of file URLs or external URLs

## Testing

### Unit Tests

**Upload Tests (`noujoumApi/tests/Feature/UploadTest.php`):**
- Single file upload
- Multiple file upload
- Authentication requirements
- File type validation
- File size validation

**App Creation Tests (`noujoumApi/tests/Feature/AppCreationWithFilesTest.php`):**
- App creation with file uploads
- App creation with URL fallbacks
- Validation requirements
- File storage verification

## Usage Examples

### Frontend (Dart)

```dart
// Upload single icon
final file = File(pickedFile.path);
final resp = await ApiService.uploadFile(file, fields: {'folder': 'app-icons'});
final iconUrl = resp['data']['url'];

// Upload multiple screenshots
final files = await _imagePicker.pickMultiImage();
for (final file in files) {
  final resp = await ApiService.uploadFile(File(file.path), 
    fields: {'folder': 'app-screenshots'});
  screenshotUrls.add(resp['data']['url']);
}
```

### Backend (PHP)

```php
// App creation with files
$validator = Validator::make($request->all(), [
    'icon_file' => 'nullable|file|mimes:jpeg,png,jpg,gif,webp|max:5120',
    'screenshot_files' => 'nullable|array|max:10',
    'screenshot_files.*' => 'file|mimes:jpeg,png,jpg,gif,webp|max:5120',
]);

// File processing
if ($request->hasFile('icon_file')) {
    $iconPath = $request->file('icon_file')->storeAs('app-icons', $filename, 'public');
    $iconUrl = Storage::url($iconPath);
}
```

## Migration Guide

### For Existing Apps
- No migration required - existing URL-based apps continue to work
- New apps can use either file uploads or URLs
- Admin interface displays both uploaded files and external URLs

### For Developers
1. Update mobile app to latest version
2. Use new upload interface in publish app screen
3. Existing URL-based workflow remains available as fallback

## Security Considerations

- File type validation prevents malicious uploads
- File size limits prevent storage abuse
- Authentication required for all upload operations
- Unique filename generation prevents conflicts
- Storage organized in secure folders

## Performance Optimizations

- CachedNetworkImage for efficient image loading
- Compressed image uploads (max 1024px width, 85% quality)
- Lazy loading in image galleries
- Error handling with fallback placeholders

## Future Enhancements

- Image compression and optimization
- CDN integration for better performance
- Bulk upload operations
- Image editing capabilities
- Automatic thumbnail generation
